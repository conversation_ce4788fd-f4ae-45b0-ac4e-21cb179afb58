<!-- Simple Top Users Component - Comic Website Design -->
<div class="top-users-container">
  <!-- Header -->
  <div class="top-users-header">
    <div class="header-title-section">
      <svg class="header-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
        <circle cx="12" cy="7" r="4" />
      </svg>
      <h3 class="header-title">Top 5 Cao Thủ</h3>
    </div>
  </div>

  <!-- Content Area -->
  <div class="top-users-content">
    <!-- Loading State -->

    <!-- Top Users List -->
    <div *ngIf="!isLoading() && hasUsers()" class="top-users-list">
      <div
        *ngFor="let user of topUsers() ;let i = index; trackBy: trackByUserId"
        class="top-user-item"
        (click)="onUserClick(user)"
      >
        <div class="user-item-content">
          <!-- Rank Badge -->
          <div class="rank-badge" [class]="getRankClass(i+1)">
            <span class="rank-number">{{ i+1 }}</span>
          </div>

          <!-- User Avatar -->
          <div class="user-avatar-container">
            <div class="user-avatar-link" [title]="user.firstName + ' ' + user.lastName">
              <img
                loading="lazy"
                class="user-avatar"
                [src]="user.avatar || getDefaultAvatar()"
                [alt]="user.firstName + ' ' + user.lastName"
                (error)="onImageError($event)"
              />
              <div class="avatar-overlay"></div>
            </div>
          </div>

          <!-- User Info -->
          <div class="user-info">
            <div class="user-main-info">
              <!-- Username -->
              <h3 class="user-name">
                <span class="user-name-link" [title]="user.firstName + ' ' + user.lastName">{{ user.firstName + ' ' + user.lastName }}</span>
              </h3>

              <!-- Level Info -->
              <div class="user-level">
                <svg class="level-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <polygon
                    points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"
                  />
                </svg>
                <span class="level-text">{{ getLevelTitle(user.experience, user.typeLevel) }}</span>
              </div>
            </div>

            <!-- User Stats -->
            <div class="user-stats">
              <div class="exp-count">
                <svg class="exp-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
                </svg>
                <span class="exp-count-text">{{ user.experience | numeral }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
